@page "/home"
@inherits HomeViewBase
@layout Barret.Web.Server.Features.Shared.Components.Layout.MainLayout

<PageTitle>Barret - Vehicle Configuration</PageTitle>

<div class="max-w-[1200px] mx-auto px-6 py-12 flex-1 flex flex-col">
    <!-- Header -->
    <header class="mb-16 text-left">
        <RadzenText TextStyle="TextStyle.H1" TagName="TagName.H1"
                    class="text-3xl font-medium"
                    style="color: var(--rz-text-title-color);">
            Vehicle Configuration System
        </RadzenText>
        <RadzenText TextStyle="TextStyle.Body1"
                    class="mt-2 text-lg"
                    style="color: var(--rz-text-secondary-color);">
            Select a vehicle type to begin configuration
        </RadzenText>
    </header>

    @if (ViewModel.IsLoading)
    {
        <div class="flex-1 flex flex-col items-center justify-center">
            <div class="h-12 w-12 border-4 rounded-full animate-spin"
                 style="border-color: var(--rz-base-300); border-top-color: var(--barret-teal-primary);"></div>
            <RadzenText TextStyle="TextStyle.Body1"
                        class="mt-4"
                        Style="color: var(--rz-text-secondary-color);">
                Loading vehicle types...
            </RadzenText>
        </div>
    }
    else
    {
        <!-- Vehicle Type Selection -->
        <div class="flex-1 flex flex-col justify-center max-w-5xl mx-auto w-full">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 auto-rows-fr">
                @foreach (var vehicleType in ViewModel.VehicleTypes)
                {
                    <RadzenCard class="h-full vehicle-type-card"
                                @onclick="@(() => NavigateTo(vehicleType.NavigationUrl))">
                        <div class="flex flex-col items-center text-center h-full p-8">
                            <div class="mb-8">
                                @if (vehicleType.Model == "All")
                                {
                                    <RadzenIcon Icon="apps"
                                               Style="font-size: 3rem;"
                                               class="vehicle-icon" />
                                }
                                else if (vehicleType.Model == "Vessel")
                                {
                                    <RadzenIcon Icon="directions_boat"
                                               Style="font-size: 3rem;"
                                               class="vehicle-icon" />
                                }
                                else if (vehicleType.Model == "Truck")
                                {
                                    <RadzenIcon Icon="local_shipping"
                                               Style="font-size: 3rem;"
                                               class="vehicle-icon" />
                                }
                                else if (vehicleType.Model == "Crane")
                                {
                                    <RadzenIcon Icon="precision_manufacturing"
                                               Style="font-size: 3rem;"
                                               class="vehicle-icon" />
                                }
                            </div>
                            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H2"
                                        class="text-xl font-medium mb-3"
                                        Style="color: var(--rz-text-title-color);">
                                @vehicleType.Name
                            </RadzenText>
                            <RadzenText TextStyle="TextStyle.Body2"
                                        class="mb-8 text-sm flex-1"
                                        Style="color: var(--rz-text-secondary-color);">
                                Configure and manage your fleet of @(vehicleType.Model.ToLower())
                            </RadzenText>
                            <RadzenButton Text="@($"Select {vehicleType.Name}")"
                                          ButtonStyle="ButtonStyle.Primary"
                                          Size="ButtonSize.Small"
                                          class="mt-auto w-full barret-btn-primary"
                                          Click="@(() => NavigateTo(vehicleType.NavigationUrl))" />
                        </div>
                    </RadzenCard>
                }
            </div>
        </div>
    }
</div>
